"""Environment configuration module.

This module handles loading environment variables from .env files and provides
a configuration object that can be used throughout the application.
"""

import os
from dataclasses import dataclass
from typing import Optional

from dotenv import load_dotenv


@dataclass
class EnvConfig:
    """Environment configuration."""

    # GCP Configuration
    project_id: str
    gcp_location: str
    gcp_summary_bucket_name: str

    # MongoDB Configuration
    mongodb_uri: str
    mongodb_db: str
    mongodb_collection: str
    mongodb_summarized_collection: str

    # Model Configuration
    model_name: str
    summarization_model: str
    embedding_model: str
    embedding_dimension: int

    # Logging Configuration
    log_level: str = "INFO"

    @classmethod
    def load(cls, env_file: Optional[str] = None) -> "EnvConfig":
        """Load configuration from environment variables.

        Args:
            env_file: Optional path to .env file. If not provided,
                     will look for .env file in current directory.

        Returns:
            EnvConfig object with loaded configuration.

        Raises:
            ValueError: If required environment variables are missing.
        """
        if not env_file:
            env_file = f".env.{os.getenv('ENV', 'local')}"

        load_dotenv(env_file)

        required_vars = [
            "GCP_PROJECT_ID",
            "GCP_LOCATION",
            "GCP_SUMMARY_BUCKET_NAME",
            "MONGODB_URI",
            "MONGODB_DB",
            "MONGODB_COLLECTION",
            "MONGODB_SUMMARIZED_COLLECTION",
            "MODEL_NAME",
            "SUMMARIZATION_MODEL",
            "EMBEDDING_MODEL",
            "EMBEDDING_DIMENSION",
        ]

        missing_vars = [var for var in required_vars if not os.getenv(var)]
        if missing_vars:
            raise ValueError(
                f"Missing required environment variables: {', '.join(missing_vars)}"
            )

        return cls(
            project_id=os.getenv("GCP_PROJECT_ID"),
            gcp_location=os.getenv("GCP_LOCATION"),
            gcp_summary_bucket_name=os.getenv("GCP_SUMMARY_BUCKET_NAME"),
            mongodb_uri=os.getenv("MONGODB_URI"),
            mongodb_db=os.getenv("MONGODB_DB"),
            mongodb_collection=os.getenv("MONGODB_COLLECTION"),
            mongodb_summarized_collection=os.getenv("MONGODB_SUMMARIZED_COLLECTION"),
            model_name=os.getenv("MODEL_NAME"),
            summarization_model=os.getenv("SUMMARIZATION_MODEL"),
            embedding_model=os.getenv("EMBEDDING_MODEL"),
            embedding_dimension=int(os.getenv("EMBEDDING_DIMENSION")),
            log_level=os.getenv("LOG_LEVEL", "INFO"),
        )
